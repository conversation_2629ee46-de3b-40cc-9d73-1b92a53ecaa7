{"name": "paydai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3050", "build": "next build", "start": "next start --port 3050", "lint": "next lint", "seed": "ts-node --project tsconfig.json src/scripts/seed-db.ts", "seed:resume": "ts-node --project tsconfig.json src/scripts/seed-user-with-resume.ts"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@cashfreepayments/cashfree-js": "^1.0.5", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "@langchain/deepseek": "^0.0.1", "@langchain/openai": "^0.5.12", "@tiptap/extension-blockquote": "^2.14.0", "@tiptap/extension-bold": "^2.14.0", "@tiptap/extension-bullet-list": "^2.14.0", "@tiptap/extension-code": "^2.14.0", "@tiptap/extension-code-block": "^2.14.0", "@tiptap/extension-document": "^2.14.0", "@tiptap/extension-heading": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-horizontal-rule": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-italic": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-ordered-list": "^2.14.0", "@tiptap/extension-paragraph": "^2.14.0", "@tiptap/extension-strike": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/form-data": "^2.2.1", "@types/node-fetch": "^2.6.12", "@types/puppeteer": "^5.4.7", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cashfree-pg": "^5.0.8", "cheerio": "^1.1.0", "css-select": "^5.2.2", "form-data": "^4.0.3", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.27", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "node-fetch": "^2.7.0", "pdf-parse": "^1.1.1", "playwright": "^1.55.0", "puppeteer": "^24.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "sass": "^1.89.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}