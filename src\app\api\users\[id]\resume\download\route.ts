import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api/error-handler';
import { withAuthAndOwnership } from '@/lib/auth/middleware';
import { generateResumePdfWithPlaywright } from '@/lib/pdf/playwright-pdf-generator';
import connectToDatabase from '@/lib/mongodb';
import UserProfile from '@/models/UserProfile';
import mongoose from 'mongoose';

/**
 * POST handler for downloading fine-tuned resume as PDF
 * @param req The request object containing the HTML content
 * @param context The route context with user ID
 * @returns A response with the PDF file
 */
export const POST = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }

      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Parse request body
      const body = await req.json();
      const { htmlContent, fileName } = body;

      if (!htmlContent) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'HTML content is required',
              code: 'missing_content',
            },
          },
          { status: 400 }
        );
      }

      // Connect to database
      await connectToDatabase();

      // Verify user exists and has resume data
      const userProfile = await UserProfile.findOne({ user: userId });
      if (!userProfile) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User profile not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Generate PDF from HTML content
      const pdfBuffer = await generateResumePdfWithPlaywright(htmlContent);

      // Generate filename
      const timestamp = new Date().toISOString().split('T')[0];
      const defaultFileName = `resume-${timestamp}.pdf`;
      const finalFileName = fileName || defaultFileName;

      // Return the PDF file
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${finalFileName}"`,
          'Content-Length': pdfBuffer.length.toString(),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      return handleApiError(error);
    }
  },
  (params) => params.id
);
