import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

export interface PdfGenerationOptions {
  format?: 'A4' | 'Letter';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}

export class PlaywrightPdfGenerator {
  private static instance: PlaywrightPdfGenerator;
  private browser: Browser | null = null;

  private constructor() {}

  public static getInstance(): PlaywrightPdfGenerator {
    if (!PlaywrightPdfGenerator.instance) {
      PlaywrightPdfGenerator.instance = new PlaywrightPdfGenerator();
    }
    return PlaywrightPdfGenerator.instance;
  }

  private async getBrowser(): Promise<Browser> {
    // Check if browser exists and is still connected
    if (!this.browser || !this.browser.isConnected()) {
      // Close existing browser if it exists but is disconnected
      if (this.browser) {
        try {
          await this.browser.close();
        } catch (error) {
          // Ignore errors when closing disconnected browser
          console.warn('Error closing disconnected browser:', error);
        }
      }

      this.browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    }
    return this.browser;
  }

  public async generatePdfFromHtml(
    htmlContent: string,
    options: PdfGenerationOptions = {},
    retryCount: number = 0
  ): Promise<Buffer> {
    const maxRetries = 2;

    try {
      const browser = await this.getBrowser();
      const page = await browser.newPage();

      try {
        // Set default options
        const defaultOptions: PdfGenerationOptions = {
          format: 'A4',
          margin: {
            top: '0.5in',
            right: '0.5in',
            bottom: '0.5in',
            left: '0.5in'
          },
          printBackground: true,
          displayHeaderFooter: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Debug: Log HTML content length and first 200 characters
        console.log('HTML content length:', htmlContent.length);
        console.log('HTML content preview:', htmlContent.substring(0, 200));

        // Set content and wait for it to load
        await page.setContent(htmlContent, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // Wait a bit more for any dynamic content to render
        await page.waitForTimeout(1000);

        // Debug: Check if content was loaded properly
        const bodyContent = await page.textContent('body');
        console.log('Body text content length after loading:', bodyContent?.length || 0);
        console.log('Body text preview:', bodyContent?.substring(0, 200) || 'No content');

        // Also check the HTML structure
        const bodyHTML = await page.innerHTML('body');
        console.log('Body HTML length:', bodyHTML?.length || 0);
        console.log('Body HTML preview:', bodyHTML?.substring(0, 300) || 'No HTML content');

        // Generate PDF
        console.log('Generating PDF with options:', {
          format: finalOptions.format,
          margin: finalOptions.margin,
          printBackground: finalOptions.printBackground,
          displayHeaderFooter: finalOptions.displayHeaderFooter
        });

        const pdfBuffer = await page.pdf({
          format: finalOptions.format as any,
          margin: finalOptions.margin,
          printBackground: finalOptions.printBackground,
          displayHeaderFooter: finalOptions.displayHeaderFooter,
          headerTemplate: finalOptions.headerTemplate,
          footerTemplate: finalOptions.footerTemplate
        });

        console.log('PDF generated successfully, buffer size:', pdfBuffer.length);
        return Buffer.from(pdfBuffer);
      } finally {
        await page.close();
      }
    } catch (error) {
      console.error(`PDF generation attempt ${retryCount + 1} failed:`, error);

      // If browser connection error and we haven't exceeded max retries
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (retryCount < maxRetries && (
        errorMessage.includes('Target page, context or browser has been closed') ||
        errorMessage.includes('Browser has been closed') ||
        errorMessage.includes('Connection closed')
      )) {
        // Force browser recreation on next attempt
        this.browser = null;
        console.log(`Retrying PDF generation (attempt ${retryCount + 2}/${maxRetries + 1})`);
        return this.generatePdfFromHtml(htmlContent, options, retryCount + 1);
      }

      throw error;
    }
  }

  public async generatePdfFromUrl(
    url: string,
    options: PdfGenerationOptions = {},
    retryCount: number = 0
  ): Promise<Buffer> {
    const maxRetries = 2;

    try {
      const browser = await this.getBrowser();
      const page = await browser.newPage();

      try {
        // Set default options
        const defaultOptions: PdfGenerationOptions = {
          format: 'A4',
          margin: {
            top: '0.5in',
            right: '0.5in',
            bottom: '0.5in',
            left: '0.5in'
          },
          printBackground: true,
          displayHeaderFooter: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Navigate to URL and wait for it to load
        await page.goto(url, {
          waitUntil: 'networkidle',
          timeout: 30000
        });

        // Generate PDF
        const pdfBuffer = await page.pdf({
          format: finalOptions.format as any,
          margin: finalOptions.margin,
          printBackground: finalOptions.printBackground,
          displayHeaderFooter: finalOptions.displayHeaderFooter,
          headerTemplate: finalOptions.headerTemplate,
          footerTemplate: finalOptions.footerTemplate
        });

        return Buffer.from(pdfBuffer);
      } finally {
        await page.close();
      }
    } catch (error) {
      console.error(`PDF generation from URL attempt ${retryCount + 1} failed:`, error);

      // If browser connection error and we haven't exceeded max retries
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (retryCount < maxRetries && (
        errorMessage.includes('Target page, context or browser has been closed') ||
        errorMessage.includes('Browser has been closed') ||
        errorMessage.includes('Connection closed')
      )) {
        // Force browser recreation on next attempt
        this.browser = null;
        console.log(`Retrying PDF generation from URL (attempt ${retryCount + 2}/${maxRetries + 1})`);
        return this.generatePdfFromUrl(url, options, retryCount + 1);
      }

      throw error;
    }
  }

  public async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}

// Export a singleton instance
export const playwrightPdfGenerator = PlaywrightPdfGenerator.getInstance();

// Utility function for generating resume PDFs with optimized styling
export async function generateResumePdfWithPlaywright(htmlContent: string): Promise<Buffer> {
  console.log('generateResumePdfWithPlaywright called with content length:', htmlContent.length);
  console.log('Raw HTML content preview:', htmlContent.substring(0, 300));

  // If htmlContent is empty or just whitespace, provide fallback content
  if (!htmlContent || htmlContent.trim().length === 0) {
    console.warn('Empty HTML content provided, using fallback content');
    htmlContent = '<h1>Resume</h1><p>No content available</p>';
  }

  // Check if the content is already a complete HTML document
  const isCompleteHtml = htmlContent.trim().toLowerCase().startsWith('<!doctype html') ||
                        htmlContent.trim().toLowerCase().startsWith('<html');

  if (isCompleteHtml) {
    console.log('Content is already a complete HTML document, using as-is');
    console.log('Complete HTML preview (first 1000 chars):', htmlContent.substring(0, 1000));

    const pdfBuffer = await playwrightPdfGenerator.generatePdfFromHtml(htmlContent, {
      format: 'A4',
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      printBackground: true
    });

    console.log('PDF buffer generated, size:', pdfBuffer.length);
    return pdfBuffer;
  }

  // Add CSS optimizations for PDF generation (only for HTML fragments)
  const optimizedHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        * {
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 20px;
          font-size: 12px;
        }
        
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5rem;
          font-weight: 600;
          line-height: 1.2;
        }
        
        h1 { font-size: 1.8rem; color: #2563eb; }
        h2 { font-size: 1.4rem; color: #1e40af; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.25rem; }
        h3 { font-size: 1.2rem; color: #374151; }
        h4 { font-size: 1rem; color: #4b5563; }
        
        p {
          margin-bottom: 0.75rem;
        }
        
        ul, ol {
          margin-bottom: 0.75rem;
          padding-left: 1.5rem;
        }
        
        li {
          margin-bottom: 0.25rem;
        }
        
        .section {
          margin-bottom: 1.5rem;
          page-break-inside: avoid;
        }
        
        .contact-info {
          text-align: center;
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 2px solid #e5e7eb;
        }
        
        .contact-info h1 {
          margin-bottom: 0.5rem;
        }
        
        .contact-details {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          gap: 1rem;
          font-size: 0.9rem;
          color: #6b7280;
        }
        
        .skills-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
        }
        
        .skill-category {
          margin-bottom: 1rem;
        }
        
        .skill-category h4 {
          margin-bottom: 0.5rem;
          color: #374151;
        }
        
        .skill-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem;
        }
        
        .skill-tag {
          background-color: #f3f4f6;
          color: #374151;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.8rem;
          border: 1px solid #d1d5db;
        }
        
        .experience-item, .education-item {
          margin-bottom: 1.5rem;
          padding-left: 1rem;
          border-left: 3px solid #e5e7eb;
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 0.5rem;
        }
        
        .item-title {
          font-weight: 600;
          color: #111827;
        }
        
        .item-company {
          color: #6b7280;
          font-weight: 500;
        }
        
        .item-date {
          color: #9ca3af;
          font-size: 0.9rem;
          text-align: right;
        }
        
        .item-location {
          color: #9ca3af;
          font-size: 0.9rem;
        }
        
        .achievements {
          margin-top: 0.5rem;
        }
        
        .achievements ul {
          margin: 0;
        }
        
        .achievements li {
          margin-bottom: 0.25rem;
          color: #4b5563;
        }
        
        @media print {
          body {
            font-size: 11px;
          }
          
          .section {
            page-break-inside: avoid;
          }
          
          h2 {
            page-break-after: avoid;
          }
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
  `;

  console.log('Optimized HTML length:', optimizedHtml.length);
  console.log('Optimized HTML preview (first 500 chars):', optimizedHtml.substring(0, 500));
  console.log('Optimized HTML preview (body section):', optimizedHtml.substring(optimizedHtml.indexOf('<body>'), optimizedHtml.indexOf('<body>') + 300));

  return await playwrightPdfGenerator.generatePdfFromHtml(optimizedHtml, {
    format: 'A4',
    margin: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    printBackground: true
  });
}

// Test function to verify PDF generation works
export async function testPlaywrightPdfGeneration(): Promise<Buffer> {
  const testHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Test PDF</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1 { color: #333; }
        p { margin: 10px 0; }
      </style>
    </head>
    <body>
      <h1>Test PDF Generation</h1>
      <p>This is a test to verify that Playwright PDF generation is working.</p>
      <p>If you can see this text in the PDF, then the basic functionality is working.</p>
      <ul>
        <li>Item 1</li>
        <li>Item 2</li>
        <li>Item 3</li>
      </ul>
    </body>
    </html>
  `;

  console.log('Testing PDF generation with simple HTML...');
  return await playwrightPdfGenerator.generatePdfFromHtml(testHtml, {
    format: 'A4',
    margin: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    printBackground: true
  });
}
