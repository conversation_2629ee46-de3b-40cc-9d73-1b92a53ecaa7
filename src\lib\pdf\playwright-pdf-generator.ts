import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

export interface PdfGenerationOptions {
  format?: 'A4' | 'Letter';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}

export class PlaywrightPdfGenerator {
  private static instance: PlaywrightPdfGenerator;
  private browser: Browser | null = null;

  private constructor() {}

  public static getInstance(): PlaywrightPdfGenerator {
    if (!PlaywrightPdfGenerator.instance) {
      PlaywrightPdfGenerator.instance = new PlaywrightPdfGenerator();
    }
    return PlaywrightPdfGenerator.instance;
  }

  private async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    }
    return this.browser;
  }

  public async generatePdfFromHtml(
    htmlContent: string,
    options: PdfGenerationOptions = {}
  ): Promise<Buffer> {
    const browser = await this.getBrowser();
    const page = await browser.newPage();

    try {
      // Set default options
      const defaultOptions: PdfGenerationOptions = {
        format: 'A4',
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        printBackground: true,
        displayHeaderFooter: false
      };

      const finalOptions = { ...defaultOptions, ...options };

      // Set content and wait for it to load
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle',
        timeout: 30000
      });

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: finalOptions.format as any,
        margin: finalOptions.margin,
        printBackground: finalOptions.printBackground,
        displayHeaderFooter: finalOptions.displayHeaderFooter,
        headerTemplate: finalOptions.headerTemplate,
        footerTemplate: finalOptions.footerTemplate
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  public async generatePdfFromUrl(
    url: string,
    options: PdfGenerationOptions = {}
  ): Promise<Buffer> {
    const browser = await this.getBrowser();
    const page = await browser.newPage();

    try {
      // Set default options
      const defaultOptions: PdfGenerationOptions = {
        format: 'A4',
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        printBackground: true,
        displayHeaderFooter: false
      };

      const finalOptions = { ...defaultOptions, ...options };

      // Navigate to URL and wait for it to load
      await page.goto(url, {
        waitUntil: 'networkidle',
        timeout: 30000
      });

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: finalOptions.format as any,
        margin: finalOptions.margin,
        printBackground: finalOptions.printBackground,
        displayHeaderFooter: finalOptions.displayHeaderFooter,
        headerTemplate: finalOptions.headerTemplate,
        footerTemplate: finalOptions.footerTemplate
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  public async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}

// Export a singleton instance
export const playwrightPdfGenerator = PlaywrightPdfGenerator.getInstance();

// Utility function for generating resume PDFs with optimized styling
export async function generateResumePdfWithPlaywright(htmlContent: string): Promise<Buffer> {
  // Add CSS optimizations for PDF generation
  const optimizedHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        * {
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 20px;
          font-size: 12px;
        }
        
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5rem;
          font-weight: 600;
          line-height: 1.2;
        }
        
        h1 { font-size: 1.8rem; color: #2563eb; }
        h2 { font-size: 1.4rem; color: #1e40af; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.25rem; }
        h3 { font-size: 1.2rem; color: #374151; }
        h4 { font-size: 1rem; color: #4b5563; }
        
        p {
          margin-bottom: 0.75rem;
        }
        
        ul, ol {
          margin-bottom: 0.75rem;
          padding-left: 1.5rem;
        }
        
        li {
          margin-bottom: 0.25rem;
        }
        
        .section {
          margin-bottom: 1.5rem;
          page-break-inside: avoid;
        }
        
        .contact-info {
          text-align: center;
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 2px solid #e5e7eb;
        }
        
        .contact-info h1 {
          margin-bottom: 0.5rem;
        }
        
        .contact-details {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          gap: 1rem;
          font-size: 0.9rem;
          color: #6b7280;
        }
        
        .skills-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
        }
        
        .skill-category {
          margin-bottom: 1rem;
        }
        
        .skill-category h4 {
          margin-bottom: 0.5rem;
          color: #374151;
        }
        
        .skill-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem;
        }
        
        .skill-tag {
          background-color: #f3f4f6;
          color: #374151;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.8rem;
          border: 1px solid #d1d5db;
        }
        
        .experience-item, .education-item {
          margin-bottom: 1.5rem;
          padding-left: 1rem;
          border-left: 3px solid #e5e7eb;
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 0.5rem;
        }
        
        .item-title {
          font-weight: 600;
          color: #111827;
        }
        
        .item-company {
          color: #6b7280;
          font-weight: 500;
        }
        
        .item-date {
          color: #9ca3af;
          font-size: 0.9rem;
          text-align: right;
        }
        
        .item-location {
          color: #9ca3af;
          font-size: 0.9rem;
        }
        
        .achievements {
          margin-top: 0.5rem;
        }
        
        .achievements ul {
          margin: 0;
        }
        
        .achievements li {
          margin-bottom: 0.25rem;
          color: #4b5563;
        }
        
        @media print {
          body {
            font-size: 11px;
          }
          
          .section {
            page-break-inside: avoid;
          }
          
          h2 {
            page-break-after: avoid;
          }
        }
      </style>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
  `;

  return await playwrightPdfGenerator.generatePdfFromHtml(optimizedHtml, {
    format: 'A4',
    margin: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    printBackground: true
  });
}
