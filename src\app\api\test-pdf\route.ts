import { NextRequest, NextResponse } from 'next/server';
import { testPlaywrightPdfGeneration } from '@/lib/pdf/playwright-pdf-generator';

export async function GET(req: NextRequest) {
  try {
    console.log('Testing PDF generation...');
    const pdfBuffer = await testPlaywrightPdfGeneration();
    
    console.log('Test PDF generated successfully, size:', pdfBuffer.length);
    
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="test.pdf"',
        'Content-Length': pdfBuffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('Test PDF generation failed:', error);
    return NextResponse.json(
      { error: 'PDF generation failed', details: error.message },
      { status: 500 }
    );
  }
}
